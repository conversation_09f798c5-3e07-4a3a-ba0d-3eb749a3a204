{"name": "atma-e2e-testing", "version": "1.0.0", "description": "End-to-End Testing Suite for ATMA Backend Services", "main": "index.js", "scripts": {"test": "node test-runner.js", "test:single": "node single-user-test.js", "test:dual": "node dual-user-test.js", "test:stress": "node stress-test.js", "test:websocket": "node websocket-test.js", "test:chatbot": "node chatbot-test.js", "test:login": "node test-login-token.js", "clean": "node cleanup.js", "check-services": "node scripts/check-services.js", "install-deps": "npm install", "setup": "chmod +x scripts/install.sh && ./scripts/install.sh", "db:setup": "node scripts/setup-test-db.js setup", "db:cleanup": "node scripts/setup-test-db.js cleanup", "test:with-db": "npm run db:setup && npm run test && npm run db:cleanup"}, "dependencies": {"@faker-js/faker": "^9.9.0", "axios": "^1.6.0", "bcrypt": "^6.0.0", "chalk": "^4.1.2", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "pg": "^8.11.0", "socket.io-client": "^4.7.2", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["e2e", "testing", "atma", "websocket", "api"], "author": "ATMA Team", "license": "MIT"}