/* PrismJS Theme - Custom Light Theme */
code[class*="language-"],
pre[class*="language-"] {
  color: #24292e;
  background: none;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.875rem;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  tab-size: 4;
  hyphens: none;
}

pre[class*="language-"]::-moz-selection,
pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection,
code[class*="language-"] ::-moz-selection {
  text-shadow: none;
  background: #b3d4fc;
}

pre[class*="language-"]::selection,
pre[class*="language-"] ::selection,
code[class*="language-"]::selection,
code[class*="language-"] ::selection {
  text-shadow: none;
  background: #b3d4fc;
}

@media print {
  code[class*="language-"],
  pre[class*="language-"] {
    text-shadow: none;
  }
}

/* Code blocks */
pre[class*="language-"] {
  padding: 1rem;
  margin: 0.5rem 0;
  overflow: auto;
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 8px;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
  background: #f6f8fa;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  white-space: normal;
  background: #f3f4f6;
  color: #e11d48;
  border: 1px solid #e5e7eb;
}

/* Tokens */
.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #6a737d;
  font-style: italic;
}

.token.punctuation {
  color: #24292e;
}

.token.namespace {
  opacity: 0.7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
  color: #005cc5;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
  color: #032f62;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
  color: #d73a49;
  background: none;
}

.token.atrule,
.token.attr-value,
.token.keyword {
  color: #d73a49;
}

.token.function,
.token.class-name {
  color: #6f42c1;
}

.token.regex,
.token.important,
.token.variable {
  color: #e36209;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.entity {
  cursor: help;
}

/* JSON specific */
.language-json .token.property {
  color: #005cc5;
}

.language-json .token.string {
  color: #032f62;
}

.language-json .token.number {
  color: #005cc5;
}

.language-json .token.boolean {
  color: #005cc5;
}

.language-json .token.null {
  color: #005cc5;
}

/* JavaScript specific */
.language-javascript .token.keyword {
  color: #d73a49;
}

.language-javascript .token.function {
  color: #6f42c1;
}

.language-javascript .token.string {
  color: #032f62;
}

/* Python specific */
.language-python .token.keyword {
  color: #d73a49;
}

.language-python .token.function {
  color: #6f42c1;
}

.language-python .token.string {
  color: #032f62;
}

.language-python .token.comment {
  color: #6a737d;
}

/* Bash specific */
.language-bash .token.function {
  color: #6f42c1;
}

.language-bash .token.string {
  color: #032f62;
}

.language-bash .token.parameter {
  color: #24292e;
}

/* HTTP specific */
.language-http .token.request-line {
  color: #d73a49;
}

.language-http .token.header-name {
  color: #005cc5;
}

.language-http .token.header-value {
  color: #032f62;
}

/* Line highlighting */
pre[class*="language-"] .highlight-line {
  background: rgba(255, 255, 0, 0.1);
  display: block;
  margin: 0 -1rem;
  padding: 0 1rem;
}

/* Line numbers */
pre[class*="language-"].line-numbers {
  position: relative;
  padding-left: 3.8rem;
  counter-reset: linenumber;
}

pre[class*="language-"].line-numbers > code {
  position: relative;
  white-space: inherit;
}

.line-numbers .line-numbers-rows {
  position: absolute;
  pointer-events: none;
  top: 0;
  font-size: 100%;
  left: -3.8rem;
  width: 3rem;
  letter-spacing: -1px;
  border-right: 1px solid #e1e4e8;
  user-select: none;
}

.line-numbers-rows > span {
  display: block;
  counter-increment: linenumber;
}

.line-numbers-rows > span:before {
  content: counter(linenumber);
  color: #6a737d;
  display: block;
  padding-right: 0.8rem;
  text-align: right;
}

/* Force light theme for consistency with application design */
code[class*="language-"],
pre[class*="language-"] {
  color: #24292e !important;
  background: none !important;
}

pre[class*="language-"] {
  background: #f6f8fa !important;
  border: 1px solid #e1e4e8 !important;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
  background: #f6f8fa !important;
}

:not(pre) > code[class*="language-"] {
  background: #f3f4f6 !important;
  color: #e11d48 !important;
  border: 1px solid #e5e7eb !important;
}

/* Dark Theme Overrides */
[data-theme="dark"] code[class*="language-"],
[data-theme="dark"] pre[class*="language-"] {
  color: #e2e8f0 !important;
  background: none !important;
}

[data-theme="dark"] pre[class*="language-"] {
  background: #1e293b !important;
  border: 1px solid #475569 !important;
}

[data-theme="dark"] :not(pre) > code[class*="language-"],
[data-theme="dark"] pre[class*="language-"] {
  background: #1e293b !important;
}

[data-theme="dark"] :not(pre) > code[class*="language-"] {
  background: #334155 !important;
  color: #fbbf24 !important;
  border: 1px solid #475569 !important;
}

/* Dark Theme Token Colors */
[data-theme="dark"] .token.comment,
[data-theme="dark"] .token.prolog,
[data-theme="dark"] .token.doctype,
[data-theme="dark"] .token.cdata {
  color: #94a3b8 !important;
  font-style: italic;
}

[data-theme="dark"] .token.punctuation {
  color: #e2e8f0 !important;
}

[data-theme="dark"] .token.property,
[data-theme="dark"] .token.tag,
[data-theme="dark"] .token.boolean,
[data-theme="dark"] .token.number,
[data-theme="dark"] .token.constant,
[data-theme="dark"] .token.symbol,
[data-theme="dark"] .token.deleted {
  color: #60a5fa !important;
}

[data-theme="dark"] .token.selector,
[data-theme="dark"] .token.attr-name,
[data-theme="dark"] .token.string,
[data-theme="dark"] .token.char,
[data-theme="dark"] .token.builtin,
[data-theme="dark"] .token.inserted {
  color: #34d399 !important;
}

[data-theme="dark"] .token.operator,
[data-theme="dark"] .token.entity,
[data-theme="dark"] .token.url,
[data-theme="dark"] .language-css .token.string,
[data-theme="dark"] .style .token.string {
  color: #f87171 !important;
  background: none !important;
}

[data-theme="dark"] .token.atrule,
[data-theme="dark"] .token.attr-value,
[data-theme="dark"] .token.keyword {
  color: #f87171 !important;
}

[data-theme="dark"] .token.function,
[data-theme="dark"] .token.class-name {
  color: #a78bfa !important;
}

[data-theme="dark"] .token.regex,
[data-theme="dark"] .token.important,
[data-theme="dark"] .token.variable {
  color: #fbbf24 !important;
}

/* Dark Theme Language Specific */
[data-theme="dark"] .language-json .token.property {
  color: #60a5fa !important;
}

[data-theme="dark"] .language-json .token.string {
  color: #34d399 !important;
}

[data-theme="dark"] .language-json .token.number {
  color: #60a5fa !important;
}

[data-theme="dark"] .language-json .token.boolean {
  color: #60a5fa !important;
}

[data-theme="dark"] .language-json .token.null {
  color: #60a5fa !important;
}

[data-theme="dark"] .language-javascript .token.keyword {
  color: #f87171 !important;
}

[data-theme="dark"] .language-javascript .token.function {
  color: #a78bfa !important;
}

[data-theme="dark"] .language-javascript .token.string {
  color: #34d399 !important;
}

[data-theme="dark"] .language-python .token.keyword {
  color: #f87171 !important;
}

[data-theme="dark"] .language-python .token.function {
  color: #a78bfa !important;
}

[data-theme="dark"] .language-python .token.string {
  color: #34d399 !important;
}

[data-theme="dark"] .language-python .token.comment {
  color: #94a3b8 !important;
}

[data-theme="dark"] .language-bash .token.function {
  color: #a78bfa !important;
}

[data-theme="dark"] .language-bash .token.string {
  color: #34d399 !important;
}

[data-theme="dark"] .language-bash .token.parameter {
  color: #e2e8f0 !important;
}

[data-theme="dark"] .language-http .token.request-line {
  color: #f87171 !important;
}

[data-theme="dark"] .language-http .token.header-name {
  color: #60a5fa !important;
}

[data-theme="dark"] .language-http .token.header-value {
  color: #34d399 !important;
}

/* Dark Theme Selection */
[data-theme="dark"] pre[class*="language-"]::-moz-selection,
[data-theme="dark"] pre[class*="language-"] ::-moz-selection,
[data-theme="dark"] code[class*="language-"]::-moz-selection,
[data-theme="dark"] code[class*="language-"] ::-moz-selection {
  text-shadow: none;
  background: #3b82f6 !important;
}

[data-theme="dark"] pre[class*="language-"]::selection,
[data-theme="dark"] pre[class*="language-"] ::selection,
[data-theme="dark"] code[class*="language-"]::selection,
[data-theme="dark"] code[class*="language-"] ::selection {
  text-shadow: none;
  background: #3b82f6 !important;
}

/* Dark Theme Line Numbers */
[data-theme="dark"] .line-numbers .line-numbers-rows {
  border-right: 1px solid #475569 !important;
}

[data-theme="dark"] .line-numbers-rows > span:before {
  color: #94a3b8 !important;
}

/* Dark Theme Line Highlighting */
[data-theme="dark"] pre[class*="language-"] .highlight-line {
  background: rgba(59, 130, 246, 0.1) !important;
}
