{"name": "atma-analysis-worker", "version": "1.0.0", "description": "Analysis Worker for ATMA Backend - Processes assessment data using AI", "main": "src/worker.js", "scripts": {"start": "node src/worker.js", "dev": "nodemon src/worker.js", "dev:mock": "copy .env.testing .env && nodemon src/worker.js", "start:mock": "copy .env.testing .env && node src/worker.js", "test": "jest", "test:watch": "jest --watch", "test:integration": "jest --testPathPattern=integration", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"@google/genai": "^0.3.0", "amqplib": "^0.10.3", "axios": "^1.4.0", "dotenv": "^16.0.3", "joi": "^17.9.2", "uuid": "^9.0.0", "winston": "^3.8.2"}, "devDependencies": {"eslint": "^8.39.0", "jest": "^29.5.0", "nodemon": "^3.1.10"}, "keywords": ["worker", "ai", "analysis", "rabbitmq", "google-ai", "microservices", "nodejs"], "author": "ATMA Team", "license": "MIT"}