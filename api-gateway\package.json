{"name": "atma-api-gateway", "version": "1.0.0", "description": "API Gateway for ATMA (AI-Driven Talent Mapping Assessment) Backend Services", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["api-gateway", "express", "microservices", "atma", "talent-mapping"], "author": "ATMA Team", "license": "MIT", "dependencies": {"axios": "^1.6.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "http-proxy-middleware": "^2.0.6", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}